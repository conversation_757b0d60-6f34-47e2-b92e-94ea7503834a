#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Excel档案数据转SQL生成器
专门处理材料检验档案的分级数据
"""

import pandas as pd
from datetime import datetime

def generate_simple_archives_sql(excel_file_path):
    """
    根据你的Excel数据生成SQL语句
    假设Excel结构：一级分类 | 二级分类 | 三级分类 | 四级分类
    """
    
    # 读取Excel文件
    df = None
    if excel_file_path:
        try:
            df = pd.read_excel(excel_file_path)
            print(f"成功读取Excel文件，共 {len(df)} 行数据")
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            print("将使用预定义数据生成SQL")
            df = None
    
    sql_statements = []
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 根据你的描述，生成固定的层级结构
    
    # 1. 生成一级分类（根节点）
    sql_statements.append(
        f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
        f"VALUES ('C01', 'C', 'C01', '材料检验档案', '0', 'system', 'system', '{current_time}', '{current_time}', 0);"
    )

    # 2. 生成二级分类
    sql_statements.append(
        f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
        f"VALUES ('C0101', 'C01', 'C0101', '材料及制品检验报告', '0', 'system', 'system', '{current_time}', '{current_time}', 0);"
    )
    
    # 3. 生成四级分类（具体的检验报告）
    # 根据你提供的Excel数据，这些是四级分类的具体项目
    level4_items = [
        "水泥检验报告",
        "混凝土用砂检验报告", 
        "混凝土用石检验报告",
        "混凝土用水检验报告",
        "聚羧酸系高性能减水剂检验报告",
        "混凝土外加剂检验报告",
        "粉煤灰检验报告",
        "矿渣粉检验报告",
        "混凝土配合比设计报告",
        "混凝土中氯离子含量检验报告",
        "预拌混凝土出厂质证明书",
        "混凝土试件抗压强度检验报告",
        "混凝土抗渗等级检验报告",
        "砂浆试件抗压强度检验报告",
        "钢筋力学性能、工艺性能、重量偏差检验报告",
        "钢筋焊接接头检验报告",
        "钢筋连接用套筒灌浆料检验报告",
        "钢筋套筒灌浆连接接头工艺检验报告",
        "钢筋套筒灌浆连接接头检验报告",
        "钢筋套筒灌浆连接接头试件型式检验报告",
        "预应力钢绞线力学性能检验报告",
        "预应力管道用胶泥、水泥砂浆检验报告"
    ]
    
    # 如果Excel中有数据，从Excel读取四级分类
    if df is not None and not df.empty and len(df.columns) >= 4:
        # 假设第4列是四级分类
        excel_level4_items = []
        for index, row in df.iterrows():
            level4_name = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
            if level4_name and level4_name != "nan" and level4_name not in excel_level4_items:
                excel_level4_items.append(level4_name)

        if excel_level4_items:
            level4_items = excel_level4_items
            print(f"从Excel读取到 {len(level4_items)} 个四级分类项目")
    
    # 生成四级分类的SQL语句
    for i, item_name in enumerate(level4_items, 1):
        archives_id = f"C010101{i:02d}"  # C01010101, C01010102, ...
        sql_statements.append(
            f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
            f"VALUES ('{archives_id}', 'C0101', '{archives_id}', '{item_name}', '1', 'system', 'system', '{current_time}', '{current_time}', 0);"
        )
    
    return sql_statements

def generate_predefined_sql():
    """
    生成预定义的SQL语句（当没有Excel文件时使用）
    """
    sql_statements = []
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 1. 生成一级分类（根节点）
    sql_statements.append(
        f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
        f"VALUES ('C01', 'C', 'C01', '材料检验档案', '0', 'system', 'system', '{current_time}', '{current_time}', 0);"
    )

    # 2. 生成二级分类
    sql_statements.append(
        f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
        f"VALUES ('C0101', 'C01', 'C0101', '材料及制品检验报告', '0', 'system', 'system', '{current_time}', '{current_time}', 0);"
    )

    # 3. 生成四级分类（具体的检验报告）
    level4_items = [
        "水泥检验报告",
        "混凝土用砂检验报告",
        "混凝土用石检验报告",
        "混凝土用水检验报告",
        "聚羧酸系高性能减水剂检验报告",
        "混凝土外加剂检验报告",
        "粉煤灰检验报告",
        "矿渣粉检验报告",
        "混凝土配合比设计报告",
        "混凝土中氯离子含量检验报告",
        "预拌混凝土出厂质证明书",
        "混凝土试件抗压强度检验报告",
        "混凝土抗渗等级检验报告",
        "砂浆试件抗压强度检验报告",
        "钢筋力学性能、工艺性能、重量偏差检验报告",
        "钢筋焊接接头检验报告",
        "钢筋连接用套筒灌浆料检验报告",
        "钢筋套筒灌浆连接接头工艺检验报告",
        "钢筋套筒灌浆连接接头检验报告",
        "钢筋套筒灌浆连接接头试件型式检验报告",
        "预应力钢绞线力学性能检验报告",
        "预应力管道用胶泥、水泥砂浆检验报告"
    ]

    # 生成四级分类的SQL语句
    for i, item_name in enumerate(level4_items, 1):
        archives_id = f"C010101{i:02d}"  # C01010101, C01010102, ...
        sql_statements.append(
            f"INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) "
            f"VALUES ('{archives_id}', 'C0101', '{archives_id}', '{item_name}', '1', 'system', 'system', '{current_time}', '{current_time}', 0);"
        )

    return sql_statements

def save_sql_to_file(sql_statements, output_file="archives_insert.sql"):
    """
    将SQL语句保存到文件
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 档案表数据插入SQL语句\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n")
        f.write("-- 表结构: dgdoc_archives\n\n")
        
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"SQL语句已保存到: {output_file}")

def main():
    """
    主函数 - 可以直接运行或者导入使用
    """
    # 如果没有Excel文件，使用预定义的数据生成SQL
    excel_file = "archives.xlsx"  # 你的Excel文件名

    try:
        sql_statements = generate_simple_archives_sql(excel_file)
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        print("使用预定义数据生成SQL")
        # 直接生成预定义的SQL语句
        sql_statements = generate_predefined_sql()

    if sql_statements:
        # 保存到文件
        save_sql_to_file(sql_statements)

        # 显示生成的SQL语句
        print(f"\n成功生成 {len(sql_statements)} 条SQL语句:")
        print("=" * 80)
        for i, sql in enumerate(sql_statements, 1):
            print(f"{i:2d}. {sql}")
        print("=" * 80)
    else:
        print("未生成任何SQL语句")

if __name__ == "__main__":
    main()
