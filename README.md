# Excel档案数据转SQL生成器

这个工具可以帮你将Excel中的分级档案数据转换为MySQL的INSERT语句。

## 环境设置

✅ **虚拟环境已创建并配置完成**
- Python虚拟环境：`venv/`
- 已安装依赖：`pandas`, `openpyxl`

## 文件说明

1. **excel_to_sql.py** - 通用版本，可以处理各种Excel格式
2. **simple_excel_to_sql.py** - 简化版本，专门处理你的材料检验档案数据
3. **README.md** - 使用说明文档
4. **activate_env.sh** - 激活虚拟环境的脚本
5. **venv/** - Python虚拟环境目录

## 数据库表结构

```sql
create table dgdoc_archives
(
    archives_id   varchar(64)          not null comment '档案ID'
        primary key,
    parent_id     varchar(64)          null comment '档案父级ID',
    archives_no   varchar(255)         null comment '档案编号',
    archives_name varchar(255)         null comment '档案名称',
    archives_type char                 null comment '档案类型 0-项目档案 1-单体档案',
    create_by     varchar(64)          null comment '创建者',
    update_by     varchar(64)          null comment '更新者',
    create_time   timestamp            null comment '创建时间',
    update_time   timestamp            null comment '更新时间',
    is_sz         tinyint(1) default 0 null comment '是否市政档案目录'
)
    comment '基础档案表' engine = InnoDB;
```

## 编码规则

- **虚拟父节点**: C, D, E, F... (仅作为parent_id使用)
- **一级分类**: C01, D01, E01... (parent_id为对应字母)
- **二级分类**: C0101, C0102, D0101... (parent_id为一级分类ID)
- **三级分类**: C010101, C010102... (parent_id为二级分类ID)
- **四级分类**: C01010101, C01010102... (parent_id为三级分类ID)

## 快速开始

### 步骤1: 激活虚拟环境

```bash
# 方法1: 使用脚本（推荐）
./activate_env.sh

# 方法2: 手动激活
source venv/bin/activate
```

### 步骤2: 运行程序

```bash
# 使用简化版本（推荐）- 生成预定义的材料检验档案SQL
python simple_excel_to_sql.py

# 使用通用版本 - 处理自定义Excel文件
python excel_to_sql.py your_excel_file.xlsx

# 指定工作表
python excel_to_sql.py your_excel_file.xlsx Sheet1
```

### 步骤3: 查看结果

程序会生成 `archives_insert.sql` 文件，包含所有的INSERT语句。

### 方法3: 在Python代码中使用

```python
from simple_excel_to_sql import generate_simple_archives_sql, save_sql_to_file

# 生成SQL语句
sql_statements = generate_simple_archives_sql("your_file.xlsx")

# 保存到文件
save_sql_to_file(sql_statements, "output.sql")

# 打印SQL语句
for sql in sql_statements:
    print(sql)
```

## 生成的SQL示例

```sql
-- 一级分类（根节点）
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C01', NULL, 'C01', '材料检验档案', '0', 'system', 'system', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0);

-- 二级分类
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C0101', 'C01', 'C0101', '材料及制品检验报告', '0', 'system', 'system', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0);

-- 四级分类（具体档案）
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C01010101', 'C0101', 'C01010101', '水泥检验报告', '1', 'system', 'system', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0);
```

## Excel文件格式要求

Excel文件应该包含以下列（按顺序）：
1. 一级分类
2. 二级分类  
3. 三级分类
4. 四级分类

示例：
| 一级分类 | 二级分类 | 三级分类 | 四级分类 |
|---------|---------|---------|---------|
|         | 材料及制品检验报告 |         | 水泥检验报告 |
|         | 材料及制品检验报告 |         | 混凝土用砂检验报告 |

## 依赖安装

```bash
pip install pandas openpyxl
```

## 注意事项

1. 确保Excel文件格式正确
2. 生成的SQL文件编码为UTF-8
3. 时间戳会自动设置为当前时间
4. archives_type: 0表示项目档案，1表示单体档案
5. 根据你的需求，可能需要调整编码规则或分类层级
