# Excel转SQL工具使用总结

## ✅ 环境配置完成

### 已创建的文件：
1. **`simple_excel_to_sql.py`** - 简化版程序，生成预定义的材料检验档案SQL
2. **`excel_to_sql.py`** - 通用版程序，可处理任意Excel文件
3. **`activate_env.sh`** - 虚拟环境激活脚本
4. **`README.md`** - 详细使用说明
5. **`venv/`** - Python虚拟环境（已安装pandas和openpyxl）

### 已生成的SQL文件：
- **`archives_insert.sql`** - 包含所有INSERT语句的SQL文件

## 🚀 测试结果

### 简化版程序测试：
- ✅ 成功生成 **24条** 预定义的材料检验档案SQL语句
- ✅ 包含完整的层级结构：C01 → C0101 → C01010101...

### 通用版程序测试：
- ✅ 成功处理你的Excel文件：`副本房屋建筑工程分部验收审查表20250815.xlsx`
- ✅ 读取了 **1798行** 数据，4个列（一级分类、二级分类、三级分类、四级分类）
- ✅ 生成了 **1944条** SQL语句

## 📋 使用方法

### 1. 激活虚拟环境
```bash
./activate_env.sh
# 或者
source venv/bin/activate
```

### 2. 运行程序
```bash
# 生成预定义的材料检验档案SQL（24条语句）
python simple_excel_to_sql.py

# 处理你的Excel文件（生成1944条语句）
python excel_to_sql.py "副本房屋建筑工程分部验收审查表20250815.xlsx"

# 处理其他Excel文件
python excel_to_sql.py your_file.xlsx
```

### 3. 查看结果
生成的SQL文件：`archives_insert.sql`

## 📊 生成的SQL结构

### 编码规则：
- **一级分类**: C01, C02, C03...
- **二级分类**: C0101, C0102, C0201...
- **三级分类**: C010101, C010102...
- **四级分类**: C01010101, C01010102...

### 数据库字段：
- `archives_id`: 档案ID（主键）
- `parent_id`: 父级ID
- `archives_no`: 档案编号（与archives_id相同）
- `archives_name`: 档案名称
- `archives_type`: 档案类型（0-项目档案，1-单体档案）
- `create_by`, `update_by`: 创建者/更新者（system）
- `create_time`, `update_time`: 时间戳
- `is_sz`: 是否市政档案（0）

## 🎯 SQL语句示例

```sql
-- 一级分类
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C01', NULL, 'C01', '材料检验档案', '0', 'system', 'system', '2025-08-20 15:30:48', '2025-08-20 15:30:48', 0);

-- 二级分类
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C0101', 'C01', 'C0101', '材料及制品检验报告', '0', 'system', 'system', '2025-08-20 15:30:48', '2025-08-20 15:30:48', 0);

-- 四级分类（具体档案）
INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('C01010101', 'C0101', 'C01010101', '水泥检验报告', '1', 'system', 'system', '2025-08-20 15:30:48', '2025-08-20 15:30:48', 0);
```

## 💡 使用建议

1. **首次使用**：建议先运行简化版程序熟悉输出格式
2. **处理新Excel**：使用通用版程序处理你的具体Excel文件
3. **数据验证**：执行SQL前建议先在测试环境验证
4. **备份数据**：执行INSERT前记得备份现有数据库

## 🔧 故障排除

如果遇到问题：
1. 确保虚拟环境已激活：`source venv/bin/activate`
2. 检查Excel文件路径是否正确
3. 确保Excel文件格式正确（包含4列：一级分类、二级分类、三级分类、四级分类）
4. 查看生成的SQL文件确认格式正确

## 📞 支持

如需修改编码规则、添加新功能或处理其他格式的Excel文件，可以基于现有代码进行调整。
