#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查SQL文件中是否存在重复的archives_id
"""

import re
from collections import Counter

def check_duplicate_archives_id(sql_file_path):
    """
    检查SQL文件中是否存在重复的archives_id
    """
    archives_ids = []
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取所有的archives_id
        # 匹配模式：VALUES ('archives_id', ...
        pattern = r"VALUES\s*\(\s*'([^']+)'"
        matches = re.findall(pattern, content)
        
        archives_ids = matches
        print(f"总共找到 {len(archives_ids)} 个archives_id")
        
        # 统计重复
        id_counts = Counter(archives_ids)
        duplicates = {aid: count for aid, count in id_counts.items() if count > 1}
        
        if duplicates:
            print(f"\n❌ 发现 {len(duplicates)} 个重复的archives_id:")
            print("=" * 60)
            for aid, count in sorted(duplicates.items()):
                print(f"archives_id: {aid} - 出现次数: {count}")
            
            # 显示重复ID的详细信息
            print("\n重复记录的详细信息:")
            print("=" * 60)
            
            lines = content.split('\n')
            for aid in sorted(duplicates.keys()):
                print(f"\n🔍 archives_id '{aid}' 的所有记录:")
                for i, line in enumerate(lines, 1):
                    if f"VALUES ('{aid}'" in line:
                        # 提取archives_name
                        name_match = re.search(r"'([^']+)',\s*'[01]',", line)
                        name = name_match.group(1) if name_match else "未知"
                        print(f"  第{i}行: {name}")
        else:
            print("\n✅ 没有发现重复的archives_id")
        
        # 检查ID格式是否正确
        print(f"\n📊 ID格式分析:")
        print("=" * 60)
        
        format_stats = {}
        invalid_ids = []
        
        for aid in archives_ids:
            if not aid or aid == 'None' or 'None' in aid:
                invalid_ids.append(aid)
                continue
                
            # 分析ID格式
            if len(aid) == 1:  # 单字母 (如 C, D, E)
                format_type = "单字母"
            elif len(aid) == 3:  # 三位 (如 C01, D01)
                format_type = "三位格式"
            elif len(aid) == 5:  # 五位 (如 C0101, D0102)
                format_type = "五位格式"
            elif len(aid) == 7:  # 七位 (如 C010101, D010201)
                format_type = "七位格式"
            elif len(aid) == 9:  # 九位 (如 C01010101)
                format_type = "九位格式"
            else:
                format_type = f"其他({len(aid)}位)"
            
            format_stats[format_type] = format_stats.get(format_type, 0) + 1
        
        for fmt, count in sorted(format_stats.items()):
            print(f"{fmt}: {count} 个")
        
        if invalid_ids:
            print(f"\n❌ 发现 {len(invalid_ids)} 个无效的archives_id:")
            for aid in set(invalid_ids):
                count = invalid_ids.count(aid)
                print(f"  '{aid}' - 出现次数: {count}")
        
        return len(duplicates) == 0 and len(invalid_ids) == 0
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False

def main():
    """
    主函数
    """
    sql_file = "archives_insert.sql"
    
    print("🔍 检查SQL文件中的archives_id重复情况")
    print("=" * 60)
    
    is_valid = check_duplicate_archives_id(sql_file)
    
    print("\n" + "=" * 60)
    if is_valid:
        print("✅ SQL文件检查通过，没有发现问题")
    else:
        print("❌ SQL文件存在问题，需要修复")

if __name__ == "__main__":
    main()
