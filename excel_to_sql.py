#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel档案分类数据转SQL插入语句生成器
根据Excel中的分级档案数据生成dgdoc_archives表的INSERT语句
"""

import pandas as pd
import sys
from datetime import datetime

def generate_archives_sql(excel_file_path, sheet_name=0):
    """
    从Excel文件生成档案表的SQL插入语句
    
    Args:
        excel_file_path: Excel文件路径
        sheet_name: 工作表名称或索引，默认为0（第一个工作表）
    
    Returns:
        list: SQL插入语句列表
    """
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        print(f"成功读取Excel文件: {excel_file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []
    
    sql_statements = []
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 假设Excel列结构为：一级分类、二级分类、三级分类、四级分类
    # 根据你提供的图片，列名可能是中文
    columns = df.columns.tolist()
    
    # 处理数据，生成层级结构
    level1_counter = 0  # 从0开始，对应字母A=0, B=1, C=2...
    level2_counter = 1
    level3_counter = 1
    level4_counter = 1
    
    # 存储已处理的分类，避免重复
    processed_categories = set()

    # 存储当前的层级上下文，用于处理nan值
    current_context = {"level1": "", "level2": "", "level3": "", "level4": ""}

    # 全局的path_ids字典，存储每个路径对应的archives_id
    global_path_ids = {}

    # 遍历每一行数据
    for index, row in df.iterrows():
        # 获取各级分类数据，处理特殊值
        level1 = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
        level2 = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
        level3 = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
        level4 = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""

        # 处理特殊情况：如果值是"nan"，转换为空字符串
        if level1 == "nan": level1 = ""
        if level2 == "nan": level2 = ""
        if level3 == "nan": level3 = ""
        if level4 == "nan": level4 = ""

        # 更新上下文：如果当前级有值，更新上下文；如果为空，使用上下文中的值
        if level1:
            current_context["level1"] = level1
        elif current_context["level1"]:
            level1 = current_context["level1"]

        if level2:
            current_context["level2"] = level2
        elif current_context["level2"]:
            level2 = current_context["level2"]

        if level3:
            current_context["level3"] = level3
        elif current_context["level3"]:
            level3 = current_context["level3"]

        if level4:
            current_context["level4"] = level4

        # 如果第四级为空，跳过这一行
        if not level4:
            continue

        # 构建层级路径，如果某级是"/"则将下一级数据提升到当前级
        levels = []
        level_names = [level1, level2, level3, level4]

        # 处理层级提升逻辑：如果某级是"/"，则跳过该级，后续级别提升
        effective_levels = []
        for i, level_name in enumerate(level_names):
            if not level_name:  # 遇到空值，停止处理
                break
            if level_name != "/":  # 有效数据
                effective_levels.append(level_name)

        # 如果没有有效的层级，跳过这一行
        if not effective_levels:
            continue

        # 将有效层级按顺序分配到1、2、3、4级
        for i, level_name in enumerate(effective_levels):
            levels.append((i+1, level_name))

        # 构建完整的层级结构
        current_parent_id = None

        for level_index, (level_num, level_name) in enumerate(levels):
            # 构建唯一的层级键，包含完整路径信息
            path_key = "_".join([f"L{ln}:{ln_name}" for ln, ln_name in levels[:level_index+1]])

            if path_key not in processed_categories:
                # 根据层级位置生成ID
                if level_index == 0:  # 第一级（顶级分类）
                    letter = chr(ord('C') + level1_counter)
                    archives_id = f"{letter}01"
                    parent_id = f"'{letter}'"
                    level1_counter += 1
                    current_parent_id = archives_id
                elif level_index == 1:  # 第二级
                    archives_id = f"{current_parent_id}{level2_counter:02d}"
                    parent_id = f"'{current_parent_id}'"
                    level2_counter += 1
                    current_parent_id = archives_id
                elif level_index == 2:  # 第三级
                    archives_id = f"{current_parent_id}{level3_counter:02d}"
                    parent_id = f"'{current_parent_id}'"
                    level3_counter += 1
                    current_parent_id = archives_id
                elif level_index == 3:  # 第四级
                    archives_id = f"{current_parent_id}{level4_counter:02d}"
                    parent_id = f"'{current_parent_id}'"
                    level4_counter += 1
                    current_parent_id = archives_id

                # 确定档案类型：最后一级为单体档案(1)，其他为项目档案(0)
                archives_type = "1" if level_index == len(levels) - 1 else "0"

                sql = generate_insert_sql(
                    archives_id=archives_id,
                    parent_id=parent_id,
                    archives_no=archives_id,
                    archives_name=level_name,
                    archives_type=archives_type,
                    create_time=current_time
                )
                sql_statements.append(sql)
                processed_categories.add(path_key)
                global_path_ids[path_key] = archives_id
            else:
                # 如果已存在，获取其ID并更新current_parent_id
                if path_key in global_path_ids:
                    current_parent_id = global_path_ids[path_key]

    
    return sql_statements

def generate_insert_sql(archives_id, parent_id, archives_no, archives_name, 
                       archives_type="0", create_by="'system'", update_by="'system'", 
                       create_time=None, update_time=None, is_sz=0):
    """
    生成单条INSERT SQL语句
    """
    if create_time is None:
        create_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if update_time is None:
        update_time = create_time
    
    # 处理NULL值
    parent_id_str = "NULL" if parent_id == "NULL" else parent_id
    
    sql = f"""INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('{archives_id}', {parent_id_str}, '{archives_no}', '{archives_name}', '{archives_type}', {create_by}, {update_by}, '{create_time}', '{update_time}', {is_sz});"""
    
    return sql

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python excel_to_sql.py <excel_file_path> [sheet_name]")
        print("示例: python excel_to_sql.py archives.xlsx")
        print("示例: python excel_to_sql.py archives.xlsx Sheet1")
        return
    
    excel_file = sys.argv[1]
    sheet_name = sys.argv[2] if len(sys.argv) > 2 else 0
    
    # 生成SQL语句
    sql_statements = generate_archives_sql(excel_file, sheet_name)
    
    if not sql_statements:
        print("未生成任何SQL语句，请检查Excel文件格式")
        return
    
    # 输出到文件
    output_file = "archives_insert.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 档案表数据插入SQL语句\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"成功生成 {len(sql_statements)} 条SQL语句")
    print(f"输出文件: {output_file}")
    
    # 同时在控制台显示前几条SQL语句作为预览
    print("\n前5条SQL语句预览:")
    for i, sql in enumerate(sql_statements[:5]):
        print(f"{i+1}. {sql}")
    
    if len(sql_statements) > 5:
        print(f"... 还有 {len(sql_statements) - 5} 条语句")

if __name__ == "__main__":
    main()
