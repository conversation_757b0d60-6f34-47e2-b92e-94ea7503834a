#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel档案分类数据转SQL插入语句生成器
根据Excel中的分级档案数据生成dgdoc_archives表的INSERT语句
"""

import pandas as pd
import sys
from datetime import datetime

def generate_archives_sql(excel_file_path, sheet_name=0):
    """
    从Excel文件生成档案表的SQL插入语句
    
    Args:
        excel_file_path: Excel文件路径
        sheet_name: 工作表名称或索引，默认为0（第一个工作表）
    
    Returns:
        list: SQL插入语句列表
    """
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        print(f"成功读取Excel文件: {excel_file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []
    
    sql_statements = []
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 假设Excel列结构为：一级分类、二级分类、三级分类、四级分类
    # 根据你提供的图片，列名可能是中文
    columns = df.columns.tolist()
    
    # 处理数据，生成层级结构
    level1_counter = 0  # 从0开始，对应字母A=0, B=1, C=2...
    level2_counter = 1
    level3_counter = 1
    level4_counter = 1
    
    # 存储已处理的分类，避免重复
    processed_categories = set()
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 获取各级分类数据，处理特殊值
        level1 = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
        level2 = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
        level3 = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
        level4 = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""

        # 处理特殊情况：如果值是"nan"，转换为空字符串
        if level1 == "nan": level1 = ""
        if level2 == "nan": level2 = ""
        if level3 == "nan": level3 = ""
        if level4 == "nan": level4 = ""

        # 如果某一级是"/"，表示该级没有内容，继续查找下一级
        # 如果某一级是空值，则中断查找，直接生成SQL

        # 构建层级路径，跳过"/"值，遇到空值则停止
        levels = []
        level_names = [level1, level2, level3, level4]

        # 找到所有有效的层级（非空且非"/"）
        for i, level_name in enumerate(level_names):
            if not level_name:  # 遇到空值，停止处理
                break
            if level_name != "/":  # 跳过"/"值，但继续处理后面的层级
                levels.append((i+1, level_name))

        # 如果没有有效的层级，跳过这一行
        if not levels:
            continue

        # 构建完整的层级结构
        # 需要根据实际的层级位置来构建正确的ID
        current_parent_id = None
        path_ids = {}  # 存储每个层级的ID

        for level_index, (actual_level_num, level_name) in enumerate(levels):
            # 构建唯一的层级键，包含完整路径信息
            path_key = "_".join([f"L{ln}:{ln_name}" for ln, ln_name in levels[:level_index+1]])

            if path_key not in processed_categories:
                # 根据实际层级位置生成ID
                if actual_level_num == 1:  # 一级分类
                    letter = chr(ord('C') + level1_counter)
                    archives_id = f"{letter}01"
                    parent_id = f"'{letter}'"
                    level1_counter += 1
                elif actual_level_num == 2:  # 二级分类
                    # 找到父级ID（一级分类）
                    parent_path = "_".join([f"L{ln}:{ln_name}" for ln, ln_name in levels[:level_index]])
                    if parent_path in path_ids:
                        parent_archives_id = path_ids[parent_path]
                        archives_id = f"{parent_archives_id}{level2_counter:02d}"
                        parent_id = f"'{parent_archives_id}'"
                        level2_counter += 1
                    else:
                        continue  # 跳过，因为没有找到父级
                elif actual_level_num == 3:  # 三级分类
                    # 找到父级ID（二级分类）
                    parent_path = "_".join([f"L{ln}:{ln_name}" for ln, ln_name in levels[:level_index]])
                    if parent_path in path_ids:
                        parent_archives_id = path_ids[parent_path]
                        archives_id = f"{parent_archives_id}{level3_counter:02d}"
                        parent_id = f"'{parent_archives_id}'"
                        level3_counter += 1
                    else:
                        continue  # 跳过，因为没有找到父级
                elif actual_level_num == 4:  # 四级分类
                    # 找到父级ID（三级分类）
                    parent_path = "_".join([f"L{ln}:{ln_name}" for ln, ln_name in levels[:level_index]])
                    if parent_path in path_ids:
                        parent_archives_id = path_ids[parent_path]
                        archives_id = f"{parent_archives_id}{level4_counter:02d}"
                        parent_id = f"'{parent_archives_id}'"
                        level4_counter += 1
                    else:
                        continue  # 跳过，因为没有找到父级

                # 确定档案类型：最后一级为单体档案(1)，其他为项目档案(0)
                archives_type = "1" if level_index == len(levels) - 1 else "0"

                sql = generate_insert_sql(
                    archives_id=archives_id,
                    parent_id=parent_id,
                    archives_no=archives_id,
                    archives_name=level_name,
                    archives_type=archives_type,
                    create_time=current_time
                )
                sql_statements.append(sql)
                processed_categories.add(path_key)
                path_ids[path_key] = archives_id
            else:
                # 如果已存在，获取其ID
                path_ids[path_key] = [pid for pk, pid in path_ids.items() if pk == path_key][0]

    
    return sql_statements

def generate_insert_sql(archives_id, parent_id, archives_no, archives_name, 
                       archives_type="0", create_by="'system'", update_by="'system'", 
                       create_time=None, update_time=None, is_sz=0):
    """
    生成单条INSERT SQL语句
    """
    if create_time is None:
        create_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if update_time is None:
        update_time = create_time
    
    # 处理NULL值
    parent_id_str = "NULL" if parent_id == "NULL" else parent_id
    
    sql = f"""INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('{archives_id}', {parent_id_str}, '{archives_no}', '{archives_name}', '{archives_type}', {create_by}, {update_by}, '{create_time}', '{update_time}', {is_sz});"""
    
    return sql

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python excel_to_sql.py <excel_file_path> [sheet_name]")
        print("示例: python excel_to_sql.py archives.xlsx")
        print("示例: python excel_to_sql.py archives.xlsx Sheet1")
        return
    
    excel_file = sys.argv[1]
    sheet_name = sys.argv[2] if len(sys.argv) > 2 else 0
    
    # 生成SQL语句
    sql_statements = generate_archives_sql(excel_file, sheet_name)
    
    if not sql_statements:
        print("未生成任何SQL语句，请检查Excel文件格式")
        return
    
    # 输出到文件
    output_file = "archives_insert.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 档案表数据插入SQL语句\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"成功生成 {len(sql_statements)} 条SQL语句")
    print(f"输出文件: {output_file}")
    
    # 同时在控制台显示前几条SQL语句作为预览
    print("\n前5条SQL语句预览:")
    for i, sql in enumerate(sql_statements[:5]):
        print(f"{i+1}. {sql}")
    
    if len(sql_statements) > 5:
        print(f"... 还有 {len(sql_statements) - 5} 条语句")

if __name__ == "__main__":
    main()
