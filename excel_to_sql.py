#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel档案分类数据转SQL插入语句生成器
根据Excel中的分级档案数据生成dgdoc_archives表的INSERT语句
"""

import pandas as pd
import sys
from datetime import datetime

def generate_archives_sql(excel_file_path, sheet_name=0):
    """
    从Excel文件生成档案表的SQL插入语句
    
    Args:
        excel_file_path: Excel文件路径
        sheet_name: 工作表名称或索引，默认为0（第一个工作表）
    
    Returns:
        list: SQL插入语句列表
    """
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        print(f"成功读取Excel文件: {excel_file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []
    
    sql_statements = []
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 假设Excel列结构为：一级分类、二级分类、三级分类、四级分类
    # 根据你提供的图片，列名可能是中文
    columns = df.columns.tolist()
    
    # 处理数据，生成层级结构
    level1_counter = 0  # 从0开始，对应字母A=0, B=1, C=2...
    level2_counter = 1
    level3_counter = 1
    level4_counter = 1
    
    # 存储已处理的分类，避免重复
    processed_categories = set()
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 获取各级分类数据
        level1 = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
        level2 = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
        level3 = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
        level4 = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
        
        # 生成一级分类（如果不为空且未处理过）
        if level1 and level1 != "nan" and level1 not in processed_categories:
            archives_id = chr(ord('C') + level1_counter)  # C, D, E, F...
            sql = generate_insert_sql(
                archives_id=archives_id,
                parent_id="NULL",
                archives_no=archives_id,
                archives_name=level1,
                archives_type="0",
                create_time=current_time
            )
            sql_statements.append(sql)
            processed_categories.add(level1)
            level1_id = archives_id
            level1_counter += 1
        elif level1 and level1 != "nan":
            # 如果已处理过，找到对应的ID
            existing_index = list(processed_categories).index(level1)
            level1_id = chr(ord('C') + existing_index)
        else:
            # 如果一级分类为空，使用默认值
            level1_name = "材料检验档案"
            if level1_name not in processed_categories:
                level1_id = chr(ord('C') + level1_counter)  # C, D, E, F...
                sql = generate_insert_sql(
                    archives_id=level1_id,
                    parent_id="NULL",
                    archives_no=level1_id,
                    archives_name=level1_name,
                    archives_type="0",
                    create_time=current_time
                )
                sql_statements.append(sql)
                processed_categories.add(level1_name)
                level1_counter += 1
            else:
                level1_id = "C"  # 默认使用第一个
        
        # 生成二级分类
        if level2 and level2 != "nan":
            level2_key = f"{level1_id}_{level2}"
            if level2_key not in processed_categories:
                archives_id = f"{level1_id}{level2_counter:02d}"
                sql = generate_insert_sql(
                    archives_id=archives_id,
                    parent_id=f"'{level1_id}'",
                    archives_no=archives_id,
                    archives_name=level2,
                    archives_type="0",
                    create_time=current_time
                )
                sql_statements.append(sql)
                processed_categories.add(level2_key)
                level2_id = archives_id
                level2_counter += 1
            else:
                # 找到已存在的二级分类ID
                level2_id = f"{level1_id}{level2_counter:02d}"
        
        # 生成三级分类（如果存在）
        if level3 and level3 != "nan":
            level3_key = f"{level2_id}_{level3}"
            if level3_key not in processed_categories:
                archives_id = f"{level2_id}{level3_counter:02d}"
                sql = generate_insert_sql(
                    archives_id=archives_id,
                    parent_id=f"'{level2_id}'",
                    archives_no=archives_id,
                    archives_name=level3,
                    archives_type="0",
                    create_time=current_time
                )
                sql_statements.append(sql)
                processed_categories.add(level3_key)
                level3_id = archives_id
                level3_counter += 1
            parent_for_level4 = level3_id
        else:
            parent_for_level4 = level2_id
        
        # 生成四级分类（具体档案项）
        if level4 and level4 != "nan":
            archives_id = f"{parent_for_level4}{level4_counter:02d}"
            sql = generate_insert_sql(
                archives_id=archives_id,
                parent_id=f"'{parent_for_level4}'",
                archives_no=archives_id,
                archives_name=level4,
                archives_type="1",  # 具体档案项使用类型1
                create_time=current_time
            )
            sql_statements.append(sql)
            level4_counter += 1
    
    return sql_statements

def generate_insert_sql(archives_id, parent_id, archives_no, archives_name, 
                       archives_type="0", create_by="'system'", update_by="'system'", 
                       create_time=None, update_time=None, is_sz=0):
    """
    生成单条INSERT SQL语句
    """
    if create_time is None:
        create_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if update_time is None:
        update_time = create_time
    
    # 处理NULL值
    parent_id_str = "NULL" if parent_id == "NULL" else parent_id
    
    sql = f"""INSERT INTO dgdoc_archives (archives_id, parent_id, archives_no, archives_name, archives_type, create_by, update_by, create_time, update_time, is_sz) 
VALUES ('{archives_id}', {parent_id_str}, '{archives_no}', '{archives_name}', '{archives_type}', {create_by}, {update_by}, '{create_time}', '{update_time}', {is_sz});"""
    
    return sql

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python excel_to_sql.py <excel_file_path> [sheet_name]")
        print("示例: python excel_to_sql.py archives.xlsx")
        print("示例: python excel_to_sql.py archives.xlsx Sheet1")
        return
    
    excel_file = sys.argv[1]
    sheet_name = sys.argv[2] if len(sys.argv) > 2 else 0
    
    # 生成SQL语句
    sql_statements = generate_archives_sql(excel_file, sheet_name)
    
    if not sql_statements:
        print("未生成任何SQL语句，请检查Excel文件格式")
        return
    
    # 输出到文件
    output_file = "archives_insert.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 档案表数据插入SQL语句\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"成功生成 {len(sql_statements)} 条SQL语句")
    print(f"输出文件: {output_file}")
    
    # 同时在控制台显示前几条SQL语句作为预览
    print("\n前5条SQL语句预览:")
    for i, sql in enumerate(sql_statements[:5]):
        print(f"{i+1}. {sql}")
    
    if len(sql_statements) > 5:
        print(f"... 还有 {len(sql_statements) - 5} 条语句")

if __name__ == "__main__":
    main()
